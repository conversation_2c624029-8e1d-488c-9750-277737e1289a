import { TranslationKeys } from './types';

export const ar: TranslationKeys = {
  common: {
    successResult: 'تمت العملية بنجاح',
  },
  pages: {
    dashboard: {
      title: 'لوحة التحكم',
      teams: 'الفرق',
      agents: 'الوكلاء',
      categories: 'الفئات',
      managers: 'المديرون',
      createManager: 'إنشاء مدير',
      editManager: 'تعديل المدير',
      preferences: 'التفضيلات',
      pageOne: 'الصفحة الأولى',
      overView: 'نظرة عامة',
    },
    profile: {
      pageTitle: 'الملف الشخصي',
      knowledgeBase: 'قاعدة المعرفة',
      settings: 'الإعدادات',
    },
    auth: {
      signIn: 'تسجيل الدخول',
      signUp: 'تسجيل',
      resetPassword: 'إعادة تعيين كلمة المرور',
      newPassword: 'كلمة مرور جديدة',
    },
    error: {
      notFoundTitle: '404 الصفحة غير موجودة!',
    },
  },
  auth: {
    welcome: 'مرحبًا بك في مداد!',
    welcomeSubtitle:
      'الوصول إلى لوحة التحكم الشخصية الخاصة بك، وتتبع سير العمل، وإدارة المهام الخاصة بك.',
    joinUs: 'انضم إلينا وابدأ في أتمتة سير العمل الخاص بك!',
    joinUsSubtitle:
      'سجل اليوم للوصول إلى منصتنا، وإدارة عمليات الأتمتة، وإطلاق قوة أتمتة المهام السلسة.',
    resetPassword: 'إعادة تعيين كلمة المرور',
    resetPasswordSubtitle: 'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
    setNewPassword: 'تعيين كلمة مرور جديدة',
    setNewPasswordSubtitle: 'إنشاء كلمة مرور جديدة لحسابك.',
    newPasswordView: {
      title: 'تعيين كلمة مرور جديدة',
      subtitle: 'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
      newPassword: 'كلمة المرور الجديدة',
      newPasswordPlaceholder: 'أدخل كلمة المرور الجديدة',
      confirmPassword: 'تأكيد كلمة المرور',
      confirmPasswordPlaceholder: 'تأكيد كلمة المرور الجديدة',
      changePassword: 'تغيير كلمة المرور',
      passwordRequirements: {
        minLength: 'يجب أن تتكون من 8 أحرف على الأقل',
        hasUpperLower: 'يجب أن تتضمن أحرفًا كبيرة وصغيرة ورقمًا',
        hasSpecial: 'يجب أن تتضمن حرفًا خاصًا واحدًا على الأقل',
        noSpaces: 'يجب ألا تحتوي على مسافات في البداية أو النهاية',
      },
      footerLinks: {
        privacyPolicy: 'سياسة الخصوصية',
        termsOfUse: 'شروط الاستخدام',
        dmca: 'DMCA',
      },
    },
    forgotPassword: 'نسيت كلمة المرور؟',
    rememberPassword: 'تتذكر كلمة المرور؟',
    login: 'تسجيل الدخول',
    register: 'تسجيل',
    alreadyHaveAccount: 'هل لديك حساب بالفعل؟',
    orRegisterWith: 'أو سجل باستخدام',
    orLoginWith: 'أو سجل الدخول باستخدام',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    name: 'الاسم',
    enterYourEmail: 'أدخل بريدك الإلكتروني',
    enterYourPassword: 'أدخل كلمة المرور',
    enterYourName: 'أدخل اسمك',
    newPassword: 'كلمة المرور الجديدة',
    confirmPassword: 'تأكيد كلمة المرور',
    enterYourNewPassword: 'أدخل كلمة المرور الجديدة',
    confirmYourNewPassword: 'تأكيد كلمة المرور الجديدة',
    changePassword: 'تغيير كلمة المرور',
    send: 'إرسال',
    passwordResetSuccess: 'تم تغيير كلمة المرور بنجاح!',
    passwordValidation: {
      minLength: 'يجب أن تتكون من 8 أحرف على الأقل',
      hasUpperLower: 'يجب أن تتضمن أحرفًا كبيرة وصغيرة',
      hasNumber: 'يجب أن تتضمن رقمًا واحدًا على الأقل',
      hasSpecial: 'يجب أن تتضمن حرفًا خاصًا واحدًا على الأقل',
      noSpaces: 'يجب ألا تحتوي على مسافات في البداية أو النهاية',
      passwordsMatch: 'يجب أن تتطابق كلمات المرور',
    },
    codeSent: 'لقد أرسلنا رمزًا مكونًا من 5 أرقام إلى بريدك الإلكتروني {email}.',
    resetPasswordView: {
      title: 'إعادة تعيين كلمة المرور',
      subtitle: 'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
      emailLabel: 'البريد الإلكتروني',
      emailPlaceholder: 'أدخل بريدك الإلكتروني',
      sendButton: 'إرسال',
      rememberPassword: 'تتذكر كلمة المرور؟',
      loginLink: 'تسجيل الدخول',
      codeSentMessage: 'لقد أرسلنا رمزًا مكونًا من 5 أرقام إلى بريدك الإلكتروني {email}',
      enterCodeMessage: 'الرجاء إدخاله أدناه.',
      checkingButton: 'جارٍ التحقق...',
      checking: 'جارٍ التحقق...',
      codeVerified: 'تم التحقق من الرمز بنجاح! يتم التوجيه إلى صفحة تغيير كلمة المرور...',
      footerLinks: {
        privacyPolicy: 'سياسة الخصوصية',
        termsOfUse: 'شروط الاستخدام',
        dmca: 'DMCA',
      },
    },
  },
  error: {
    notFound: 'عذرًا، الصفحة غير موجودة!',
    notFoundDescription:
      'عذرًا، لم نتمكن من العثور على الصفحة التي تبحث عنها. ربما أخطأت في كتابة عنوان URL؟ تأكد من التحقق من التهجئة.',
    forbidden: 'لا يوجد إذن',
    forbiddenDescription:
      'الصفحة التي تحاول الوصول إليها لها وصول مقيد. يرجى الرجوع إلى مسؤول النظام.',
    serverError: '500 خطأ في الخادم الداخلي',
    serverErrorDescription: 'حدث خطأ، يرجى المحاولة مرة أخرى لاحقًا.',
    permissionDenied: 'تم رفض الإذن',
    permissionDeniedDescription: 'ليس لديك إذن للوصول إلى هذه الصفحة.',
    goToHome: 'الذهاب إلى الصفحة الرئيسية',
  },
  components: {
    searchNotFound: {
      enterKeywords: 'الرجاء إدخال كلمات البحث',
      notFound: 'غير موجود',
      noResults: 'لا توجد نتائج لـ',
      checkTypos: 'حاول التحقق من الأخطاء المطبعية أو استخدام كلمات كاملة.',
    },
    noData: {
      noDataAvailable: 'لا توجد بيانات متاحة',
    },
    notifications: {
      title: 'الإشعارات',
      markAllAsRead: 'تحديد الكل كمقروء',
      viewAll: 'عرض الكل',
      markAsRead: 'تحديد كمقروء',
      today: 'اليوم',
      yesterday: 'الأمس',
      older: 'الأقدم',
      noNotifications: 'لا توجد إشعارات',
      noNotificationsDescription: 'ستتلقى إشعارات للتحديثات والأنشطة المهمة.',
    },
    accountMenu: {
      profile: 'الملف الشخصي',
      dashboard: 'لوحة التحكم',
      settings: 'الإعدادات',
      logout: 'تسجيل الخروج',
    },
    dialogs: {
      confirmDeleteMessage: 'هل أنت متأكد؟',
      deleteTeam: 'حذف الفريق؟',
      deleteTeamConfirm: 'هل أنت متأكد أنك تريد حذف هذا الفريق؟',
      logout: 'تسجيل الخروج',
      logoutConfirm: 'هل أنت متأكد أنك تريد تسجيل الخروج؟',
    },
    buttons: {
      delete: 'حذف',
      cancel: 'إلغاء',
      save: 'حفظ',
      next: 'التالي',
      previous: 'السابق',
      create: 'إنشاء',
      update: 'تحديث',
      createNewTeam: 'إنشاء فريق جديد',
      clearAll: 'مسح الكل',
    },
    tables: {
      rowsPerPageLabel: 'صفوف لكل صفحة',
      dense: 'مضغوط',
    },
    common: {
      successResult: 'تم الانتهاء بنجاح',
      view: 'عرض',
      edit: 'تعديل',
      search: 'بحث...',
      searchFiles: 'البحث في الملفات...',
      searchServices: 'البحث في الخدمات...',
      member: 'عضو',
      members: 'أعضاء',
      created: 'تم الإنشاء',
      privacyPolicy: 'سياسة الخصوصية',
      termsOfUse: 'شروط الاستخدام',
      dmca: 'حقوق النشر DMCA',
    },
    dashboard: {
      overView: 'نظرة عامة',
      categories: 'فئات',
      teams: 'الفرق',
    },
    user: {
      title: 'مرحبًا',
    },
    header: {
      workForces: 'وورك فورسز',
    },
    navigation: {
      overview: 'نظرة عامة',
      categories: 'الفئات',
      agents: 'الوكلاء',
      teams: 'الفرق',
      managers: 'المديرون',
      users: 'المستخدمون',
      settings: 'الإعدادات',
      home: 'الرئيسية',
      projects: 'المشاريع',
      subscription: 'الاشتراك',
      security: 'الأمان',
      accountSettings: 'إعدادات الحساب',
    },
    agents: {
      adminTemplates: 'قوالب المدير',
      usersTemplates: 'قوالب المستخدم',
    },
    search: {
      placeholder: 'بحث...',
      searchFiles: 'البحث في الملفات...',
      searchServices: 'البحث في الخدمات...',
    },
    workspaces: {
      free: 'مجاني',
      pro: 'محترف',
      team1: 'الفريق 1',
      team2: 'الفريق 2',
      team3: 'الفريق 3',
    },
    profile: {
      firstName: 'الاسم الأول',
      lastName: 'اسم العائلة',
      email: 'البريد الإلكتروني',
      username: 'اسم المستخدم',
      password: 'كلمة المرور',
      confirmPassword: 'تأكيد كلمة المرور',
      keepCurrentPassword: 'اترك فارغًا للاحتفاظ بكلمة المرور الحالية',
      profileUpdated: 'تم تحديث الملف الشخصي بنجاح!',
      settings: {
        language: 'اللغة',
        chooseLanguage: 'اختر لغتك المفضلة',
        managePreferences: 'إدارة إعدادات وتفضيلات حسابك',
        theme: 'السمة',
        chooseTheme: 'اختر السمة المفضلة لديك',
        light: 'فاتح',
        dark: 'داكن',
        system: 'النظام',
        languageOptions: {
          english: 'الإنجليزية',
          arabic: 'العربية',
        },
      },
      validation: {
        firstNameRequired: 'الاسم الأول مطلوب',
        lastNameRequired: 'اسم العائلة مطلوب',
        emailValid: 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
        usernameLength: 'يجب أن يتكون اسم المستخدم من 3 أحرف على الأقل',
        passwordLength: 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
        passwordUppercase: 'يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل',
        passwordLowercase: 'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل',
        passwordNumber: 'يجب أن تحتوي كلمة المرور على رقم واحد على الأقل',
        passwordsMatch: 'كلمات المرور غير متطابقة',
      },
    },
    teams: {
      title: 'الفرق',
      form: {
        editTeam: 'تعديل الفريق',
        createNewTeam: 'إنشاء فريق جديد',
        formInstructions: 'يرجى ملء الحقول المطلوبة في كل خطوة',
        steps: {
          teamInfo: 'معلومات الفريق',
          resources: 'الموارد',
          aiModel: 'نموذج الذكاء الاصطناعي',
          members: 'الأعضاء',
          instructions: 'التعليمات',
        },
        fields: {
          teamName: 'اسم الفريق',
          description: 'الوصف',
          instructions: 'التعليمات',
        },
      },
      resources: {
        searchFolders: 'البحث في المجلدات...',
        searchFiles: 'البحث في الملفات في هذا المجلد...',
        searchAiModels: 'البحث في نماذج الذكاء الاصطناعي...',
        searchMembers: 'البحث عن الأعضاء...',
        selectedFiles: 'الملفات المحددة',
        chooseAiModel: 'اختر نموذج الذكاء الاصطناعي',
        chooseTeamMembers: 'اختر أعضاء الفريق',
      },
    },
  },
  settings: {
    title: 'الإعدادات',
    reset: 'إعادة ضبط',
    close: 'إغلاق',
    darkMode: 'الوضع المظلم',
    contrast: 'التباين',
    rtl: 'من اليمين إلى اليسار',
    compact: 'مضغوط',
    compactTooltip: 'لوحة التحكم فقط ومتاح في الشاشات الكبيرة > 1600 بكسل (xl)',
    nav: 'التنقل',
    navTooltip: 'لوحة التحكم فقط',
    layout: 'التخطيط',
    color: 'اللون',
    font: 'الخط',
    presets: 'الإعدادات المسبقة',
    exit: 'خروج',
    fullScreen: 'ملء الشاشة',
  },
  categories: {
    title: 'الفئات',
    dashboard: 'لوحة التحكم',
    createNew: 'إنشاء فئة جديدة',
    edit: 'تعديل الفئة',
    createDescription: 'اتبع هذه الخطوات لإنشاء فئتك الجديدة',
    noCategories: 'لم يتم العثور على فئات',
    createFirst: 'أنشئ أول فئة للبدء',
    tryAgain: 'يرجى المحاولة مرة أخرى لاحقًا',
    search: 'البحث في الفئات...',
    table: {
      name: 'الاسم',
      dateCreated: 'تاريخ الإنشاء',
      templatesCount: 'عدد الوكلاء',
      action: 'الإجراء',
      edit: 'تعديل',
      delete: 'حذف',
    },
    validation: {
      nameRequired: 'اسم الفئة مطلوب',
      descriptionRequired: 'الوصف مطلوب',
      descriptionMinLength: 'يجب أن يتكون الوصف من 7 أحرف على الأقل',
      iconRequired: 'يرجى اختيار أيقونة لفئتك',
      iconInvalid: 'يرجى اختيار أيقونة صالحة',
      iconFormat: 'يجب أن تكون الأيقونة بتنسيق صالح (مثل: eva:home-fill)',
      colorTypeRequired: 'يرجى اختيار لون لفئتك',
      customColorRequired: 'اللون المخصص مطلوب عند اختيار نوع اللون المخصص',
      invalidHexColor: 'يرجى إدخال لون سادس عشري صالح (مثل: #FF5733)',
      hexColorFormat: 'يجب أن يبدأ اللون السادس عشري بـ # ويحتوي على 3 أو 6 أحرف صالحة',
    },
  },
};
