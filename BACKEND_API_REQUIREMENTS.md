# Backend API Requirements for Category Management

## Move Content Feature

The frontend has been implemented with a "Move Content" feature that allows users to move all content (agents, etc.) from one category to another. However, this requires a new backend API endpoint to be implemented.

### Required API Endpoint

**Endpoint:** `PATCH /api/categories/{id}/move-content`

**Method:** PATCH

**Path Parameters:**
- `id` (number): The ID of the source category from which content will be moved

**Request Body:**
```json
{
  "destinationCategoryId": 5
}
```

**Response:**
- **Success (200):** Returns updated category information or success message
- **Error (400):** Bad request if destinationCategoryId is invalid
- **Error (404):** Source category not found
- **Error (500):** Internal server error

### Expected Behavior

1. Move all agents and related content from the source category (specified in path parameter `id`) to the destination category (specified in request body)
2. Update all references to point to the destination category
3. Optionally, the source category can be deleted if it becomes empty (business logic decision)
4. Return appropriate success/error responses

### Example Request

```http
PATCH /api/categories/2/move-content
Content-Type: application/json

{
  "destinationCategoryId": 5
}
```

### Frontend Implementation Status

✅ **Completed:**
- Move content dialog UI
- Category selection interface
- API service hook (`useMoveContent`)
- Error handling with user-friendly messages
- Loading states and confirmation dialogs

⏳ **Pending Backend Implementation:**
- The actual API endpoint that performs the move operation

### Current Behavior

When users try to use the move content feature:
1. The dialog opens and allows category selection
2. On confirmation, a PATCH request is sent to `/api/categories/{id}/move-content`
3. Since the endpoint doesn't exist, a 404 error is returned
4. The frontend shows a user-friendly message: "Move content feature is not yet implemented on the backend. Please contact your administrator."
5. Detailed error information is logged to the console for developers

### Testing the Feature

Once the backend endpoint is implemented, you can test the feature by:
1. Going to the Categories page
2. Clicking the menu (three dots) on any category row
3. Selecting "Move Content"
4. Choosing a destination category
5. Clicking "Move Content" to confirm

The feature includes proper loading states, error handling, and confirmation dialogs.
