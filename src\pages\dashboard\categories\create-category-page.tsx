import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import CreateCategoryView from 'src/sections/categories/view/create-category-view';

const CreateCategoryPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('categories.createNew')}`}</title>
      </Helmet>
      <CreateCategoryView />
    </>
  );
};

export default CreateCategoryPage;
