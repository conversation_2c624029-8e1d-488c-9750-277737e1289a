import { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Popover,
  Typography,
  IconButton,
  Pagination,
  Stack,
  CircularProgress,
  InputBase,
  Paper,
  Divider,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useFormContext } from 'react-hook-form';

// Popular icons for quick access - 500 icons from various libraries
const POPULAR_ICONS = [
  // Material Design Icons (MDI)
  'mdi:account',
  'mdi:account-circle',
  'mdi:account-group',
  'mdi:account-tie',
  'mdi:alert',
  'mdi:alert-circle',
  'mdi:arrow-left',
  'mdi:arrow-right',
  'mdi:arrow-up',
  'mdi:arrow-down',
  'mdi:attachment',
  'mdi:bank',
  'mdi:bell',
  'mdi:bell-ring',
  'mdi:book',
  'mdi:bookmark',
  'mdi:briefcase',
  'mdi:bullhorn',
  'mdi:calendar',
  'mdi:camera',
  'mdi:cart',
  'mdi:cash',
  'mdi:chart-bar',
  'mdi:chart-line',
  'mdi:check',
  'mdi:check-circle',
  'mdi:chevron-down',
  'mdi:chevron-left',
  'mdi:chevron-right',
  'mdi:chevron-up',
  'mdi:clipboard',
  'mdi:clock',
  'mdi:close',
  'mdi:close-circle',
  'mdi:cloud',
  'mdi:cloud-download',
  'mdi:cloud-upload',
  'mdi:code-braces',
  'mdi:cog',
  'mdi:comment',
  'mdi:compass',
  'mdi:content-copy',
  'mdi:content-cut',
  'mdi:content-paste',
  'mdi:credit-card',
  'mdi:database',
  'mdi:delete',
  'mdi:desktop-mac',
  'mdi:dots-horizontal',
  'mdi:dots-vertical',
  'mdi:download',
  'mdi:email',
  'mdi:eye',
  'mdi:eye-off',
  'mdi:facebook',
  'mdi:file',
  'mdi:file-document',
  'mdi:file-excel',
  'mdi:file-pdf',
  'mdi:file-word',
  'mdi:filter',
  'mdi:flag',
  'mdi:folder',
  'mdi:folder-open',
  'mdi:format-bold',
  'mdi:format-italic',
  'mdi:format-underline',
  'mdi:gift',
  'mdi:github',
  'mdi:google',
  'mdi:heart',
  'mdi:help',
  'mdi:help-circle',
  'mdi:history',
  'mdi:home',
  'mdi:image',
  'mdi:inbox',
  'mdi:information',
  'mdi:instagram',
  'mdi:key',
  'mdi:keyboard',
  'mdi:laptop',
  'mdi:lightbulb',
  'mdi:link',
  'mdi:linkedin',
  'mdi:lock',
  'mdi:lock-open',
  'mdi:magnify',
  'mdi:map',
  'mdi:map-marker',
  'mdi:menu',
  'mdi:message',
  'mdi:microphone',
  'mdi:minus',
  'mdi:minus-circle',
  'mdi:monitor',
  'mdi:music',
  'mdi:newspaper',
  'mdi:note',
  'mdi:notification',
  'mdi:package',
  'mdi:palette',
  'mdi:paperclip',
  'mdi:pause',
  'mdi:pencil',
  'mdi:phone',
  'mdi:play',
  'mdi:plus',
  'mdi:plus-circle',
  'mdi:power',
  'mdi:printer',
  'mdi:refresh',
  'mdi:rocket',
  'mdi:rss',
  'mdi:save',
  'mdi:search',
  'mdi:security',
  'mdi:send',
  'mdi:settings',
  'mdi:share',
  'mdi:shield',
  'mdi:shopping',
  'mdi:signal',
  'mdi:star',
  'mdi:stop',
  'mdi:store',
  'mdi:sync',
  'mdi:table',
  'mdi:tag',
  'mdi:target',
  'mdi:thermometer',
  'mdi:thumb-down',
  'mdi:thumb-up',
  'mdi:ticket',
  'mdi:timer',
  'mdi:tools',
  'mdi:trash-can',
  'mdi:trophy',
  'mdi:truck',
  'mdi:twitter',
  'mdi:umbrella',
  'mdi:undo',
  'mdi:update',
  'mdi:upload',
  'mdi:video',
  'mdi:view-dashboard',
  'mdi:volume-high',
  'mdi:wallet',
  'mdi:warning',
  'mdi:web',
  'mdi:wifi',
  'mdi:window-close',
  'mdi:youtube',

  // Eva Icons
  'eva:activity-fill',
  'eva:activity-outline',
  'eva:alert-circle-fill',
  'eva:alert-circle-outline',
  'eva:alert-triangle-fill',
  'eva:alert-triangle-outline',
  'eva:archive-fill',
  'eva:archive-outline',
  'eva:arrow-back-fill',
  'eva:arrow-back-outline',
  'eva:arrow-circle-down-fill',
  'eva:arrow-circle-down-outline',
  'eva:arrow-circle-left-fill',
  'eva:arrow-circle-left-outline',
  'eva:arrow-circle-right-fill',
  'eva:arrow-circle-right-outline',
  'eva:arrow-circle-up-fill',
  'eva:arrow-circle-up-outline',
  'eva:arrow-down-fill',
  'eva:arrow-down-outline',
  'eva:arrow-forward-fill',
  'eva:arrow-forward-outline',
  'eva:arrow-left-fill',
  'eva:arrow-left-outline',
  'eva:arrow-right-fill',
  'eva:arrow-right-outline',
  'eva:arrow-up-fill',
  'eva:arrow-up-outline',
  'eva:at-fill',
  'eva:at-outline',
  'eva:attach-2-fill',
  'eva:attach-2-outline',
  'eva:attach-fill',
  'eva:attach-outline',
  'eva:award-fill',
  'eva:award-outline',
  'eva:backspace-fill',
  'eva:backspace-outline',
  'eva:bar-chart-2-fill',
  'eva:bar-chart-2-outline',
  'eva:bar-chart-fill',
  'eva:bar-chart-outline',
  'eva:battery-fill',
  'eva:battery-outline',
  'eva:behance-fill',
  'eva:behance-outline',
  'eva:bell-fill',
  'eva:bell-off-fill',
  'eva:bell-off-outline',
  'eva:bell-outline',
  'eva:bluetooth-fill',
  'eva:bluetooth-outline',
  'eva:book-fill',
  'eva:book-open-fill',
  'eva:book-open-outline',
  'eva:book-outline',
  'eva:bookmark-fill',
  'eva:bookmark-outline',
  'eva:briefcase-fill',
  'eva:briefcase-outline',
  'eva:browser-fill',
  'eva:browser-outline',
  'eva:brush-fill',
  'eva:brush-outline',
  'eva:bulb-fill',
  'eva:bulb-outline',
  'eva:calendar-fill',
  'eva:calendar-outline',
  'eva:camera-fill',
  'eva:camera-outline',
  'eva:car-fill',
  'eva:car-outline',
  'eva:cast-fill',
  'eva:cast-outline',
  'eva:charging-fill',
  'eva:charging-outline',
  'eva:checkmark-circle-2-fill',
  'eva:checkmark-circle-2-outline',
  'eva:checkmark-circle-fill',
  'eva:checkmark-circle-outline',
  'eva:checkmark-fill',
  'eva:checkmark-outline',
  'eva:checkmark-square-2-fill',
  'eva:checkmark-square-2-outline',
  'eva:checkmark-square-fill',
  'eva:checkmark-square-outline',
  'eva:chevron-down-fill',
  'eva:chevron-down-outline',
  'eva:chevron-left-fill',
  'eva:chevron-left-outline',
  'eva:chevron-right-fill',
  'eva:chevron-right-outline',
  'eva:chevron-up-fill',
  'eva:chevron-up-outline',
  'eva:clipboard-fill',
  'eva:clipboard-outline',
  'eva:clock-fill',
  'eva:clock-outline',
  'eva:close-circle-fill',
  'eva:close-circle-outline',
  'eva:close-fill',
  'eva:close-outline',
  'eva:close-square-fill',
  'eva:close-square-outline',
  'eva:cloud-download-fill',
  'eva:cloud-download-outline',
  'eva:cloud-upload-fill',
  'eva:cloud-upload-outline',
  'eva:code-download-fill',
  'eva:code-download-outline',
  'eva:code-fill',
  'eva:code-outline',
  'eva:collapse-fill',
  'eva:collapse-outline',
  'eva:color-palette-fill',
  'eva:color-palette-outline',
  'eva:color-picker-fill',
  'eva:color-picker-outline',
  'eva:compass-fill',
  'eva:compass-outline',
  'eva:copy-fill',
  'eva:copy-outline',
  'eva:corner-down-left-fill',
  'eva:corner-down-left-outline',
  'eva:corner-down-right-fill',
  'eva:corner-down-right-outline',
  'eva:corner-left-down-fill',
  'eva:corner-left-down-outline',
  'eva:corner-left-up-fill',
  'eva:corner-left-up-outline',
  'eva:corner-right-down-fill',
  'eva:corner-right-down-outline',
  'eva:corner-right-up-fill',
  'eva:corner-right-up-outline',
  'eva:corner-up-left-fill',
  'eva:corner-up-left-outline',
  'eva:corner-up-right-fill',
  'eva:corner-up-right-outline',
  'eva:credit-card-fill',
  'eva:credit-card-outline',
  'eva:crop-fill',
  'eva:crop-outline',
  'eva:cube-fill',
  'eva:cube-outline',
  'eva:diagonal-arrow-left-down-fill',
  'eva:diagonal-arrow-left-down-outline',
  'eva:diagonal-arrow-left-up-fill',
  'eva:diagonal-arrow-left-up-outline',
  'eva:diagonal-arrow-right-down-fill',
  'eva:diagonal-arrow-right-down-outline',
  'eva:diagonal-arrow-right-up-fill',
  'eva:diagonal-arrow-right-up-outline',
  'eva:done-all-fill',
  'eva:done-all-outline',
  'eva:download-fill',
  'eva:download-outline',
  'eva:droplet-fill',
  'eva:droplet-outline',
  'eva:edit-2-fill',
  'eva:edit-2-outline',
  'eva:edit-fill',
  'eva:edit-outline',
  'eva:email-fill',
  'eva:email-outline',
  'eva:expand-fill',
  'eva:expand-outline',
  'eva:external-link-fill',
  'eva:external-link-outline',
  'eva:eye-fill',
  'eva:eye-off-2-fill',
  'eva:eye-off-2-outline',
  'eva:eye-off-fill',
  'eva:eye-off-outline',
  'eva:eye-outline',
  'eva:facebook-fill',
  'eva:facebook-outline',
  'eva:file-add-fill',
  'eva:file-add-outline',
  'eva:file-fill',
  'eva:file-outline',
  'eva:file-remove-fill',
  'eva:file-remove-outline',
  'eva:file-text-fill',
  'eva:file-text-outline',
  'eva:film-fill',
  'eva:film-outline',
  'eva:flag-fill',
  'eva:flag-outline',
  'eva:flash-fill',
  'eva:flash-off-fill',
  'eva:flash-off-outline',
  'eva:flash-outline',
  'eva:flip-2-fill',
  'eva:flip-2-outline',
  'eva:flip-fill',
  'eva:flip-outline',
  'eva:folder-add-fill',
  'eva:folder-add-outline',
  'eva:folder-fill',
  'eva:folder-outline',
  'eva:folder-remove-fill',
  'eva:folder-remove-outline',
  'eva:funnel-fill',
  'eva:funnel-outline',
  'eva:gift-fill',
  'eva:gift-outline',
  'eva:github-fill',
  'eva:github-outline',
  'eva:globe-2-fill',
  'eva:globe-2-outline',
  'eva:globe-3-fill',
  'eva:globe-3-outline',
  'eva:globe-fill',
  'eva:globe-outline',
  'eva:google-fill',
  'eva:google-outline',
  'eva:grid-fill',
  'eva:grid-outline',
  'eva:hard-drive-fill',
  'eva:hard-drive-outline',
  'eva:hash-fill',
  'eva:hash-outline',
  'eva:headphones-fill',
  'eva:headphones-outline',
  'eva:heart-fill',
  'eva:heart-outline',
  'eva:home-fill',
  'eva:home-outline',
  'eva:image-2-fill',
  'eva:image-2-outline',
  'eva:image-fill',
  'eva:image-outline',
  'eva:inbox-fill',
  'eva:inbox-outline',
  'eva:info-fill',
  'eva:info-outline',
  'eva:keypad-fill',
  'eva:keypad-outline',
  'eva:layers-fill',
  'eva:layers-outline',
  'eva:layout-fill',
  'eva:layout-outline',
  'eva:link-2-fill',
  'eva:link-2-outline',
  'eva:link-fill',
  'eva:link-outline',
  'eva:linkedin-fill',
  'eva:linkedin-outline',
  'eva:list-fill',
  'eva:list-outline',
  'eva:loader-outline',
  'eva:lock-fill',
  'eva:lock-outline',
  'eva:log-in-fill',
  'eva:log-in-outline',
  'eva:log-out-fill',
  'eva:log-out-outline',
  'eva:map-fill',
  'eva:map-outline',
  'eva:maximize-fill',
  'eva:maximize-outline',
  'eva:menu-2-fill',
  'eva:menu-2-outline',
  'eva:menu-arrow-fill',
  'eva:menu-arrow-outline',
  'eva:menu-fill',
  'eva:menu-outline',
  'eva:message-circle-fill',
  'eva:message-circle-outline',
  'eva:message-square-fill',
  'eva:message-square-outline',
  'eva:mic-fill',
  'eva:mic-off-fill',
  'eva:mic-off-outline',
  'eva:mic-outline',
  'eva:minimize-fill',
  'eva:minimize-outline',
  'eva:minus-circle-fill',
  'eva:minus-circle-outline',
  'eva:minus-fill',
  'eva:minus-outline',
  'eva:minus-square-fill',
  'eva:minus-square-outline',
  'eva:monitor-fill',
  'eva:monitor-outline',
  'eva:moon-fill',
  'eva:moon-outline',
  'eva:more-horizontal-fill',
  'eva:more-horizontal-outline',
  'eva:more-vertical-fill',
  'eva:more-vertical-outline',
  'eva:move-fill',
  'eva:move-outline',
  'eva:music-fill',
  'eva:music-outline',
  'eva:navigation-2-fill',
  'eva:navigation-2-outline',
  'eva:navigation-fill',
  'eva:navigation-outline',
  'eva:npm-fill',
  'eva:npm-outline',
  'eva:options-2-fill',
  'eva:options-2-outline',
  'eva:options-fill',
  'eva:options-outline',
  'eva:pantone-fill',
  'eva:pantone-outline',
  'eva:paper-plane-fill',
  'eva:paper-plane-outline',
  'eva:pause-circle-fill',
  'eva:pause-circle-outline',
  'eva:people-fill',
  'eva:people-outline',
  'eva:percent-fill',
  'eva:percent-outline',
  'eva:person-add-fill',
  'eva:person-add-outline',
  'eva:person-delete-fill',
  'eva:person-delete-outline',
  'eva:person-done-fill',
  'eva:person-done-outline',
  'eva:person-fill',
  'eva:person-outline',
  'eva:person-remove-fill',
  'eva:person-remove-outline',
  'eva:phone-call-fill',
  'eva:phone-call-outline',
  'eva:phone-fill',
  'eva:phone-missed-fill',
  'eva:phone-missed-outline',
  'eva:phone-off-fill',
  'eva:phone-off-outline',
  'eva:phone-outline',
  'eva:pie-chart-2-fill',
  'eva:pie-chart-2-outline',
  'eva:pie-chart-fill',
  'eva:pie-chart-outline',
  'eva:pin-fill',
  'eva:pin-outline',
  'eva:play-circle-fill',
  'eva:play-circle-outline',
  'eva:plus-circle-fill',
  'eva:plus-circle-outline',
  'eva:plus-fill',
  'eva:plus-outline',
  'eva:plus-square-fill',
  'eva:plus-square-outline',
  'eva:power-fill',
  'eva:power-outline',
  'eva:pricetags-fill',
  'eva:pricetags-outline',
  'eva:printer-fill',
  'eva:printer-outline',
  'eva:question-mark-circle-fill',
  'eva:question-mark-circle-outline',
  'eva:question-mark-fill',
  'eva:question-mark-outline',
  'eva:radio-button-off-fill',
  'eva:radio-button-off-outline',
  'eva:radio-button-on-fill',
  'eva:radio-button-on-outline',
  'eva:radio-fill',
  'eva:radio-outline',
  'eva:recording-fill',
  'eva:recording-outline',
  'eva:refresh-fill',
  'eva:refresh-outline',
  'eva:repeat-fill',
  'eva:repeat-outline',
  'eva:rewind-left-fill',
  'eva:rewind-left-outline',
  'eva:rewind-right-fill',
  'eva:rewind-right-outline',
  'eva:save-fill',
  'eva:save-outline',
  'eva:scissors-fill',
  'eva:scissors-outline',
  'eva:search-fill',
  'eva:search-outline',
  'eva:settings-2-fill',
  'eva:settings-2-outline',
  'eva:settings-fill',
  'eva:settings-outline',
  'eva:shake-fill',
  'eva:shake-outline',
  'eva:share-fill',
  'eva:share-outline',
  'eva:shield-fill',
  'eva:shield-off-fill',
  'eva:shield-off-outline',
  'eva:shield-outline',
  'eva:shopping-bag-fill',
  'eva:shopping-bag-outline',
  'eva:shopping-cart-fill',
  'eva:shopping-cart-outline',
  'eva:shuffle-2-fill',
  'eva:shuffle-2-outline',
  'eva:shuffle-fill',
  'eva:shuffle-outline',
  'eva:skip-back-fill',
  'eva:skip-back-outline',
  'eva:skip-forward-fill',
  'eva:skip-forward-outline',
  'eva:slash-fill',
  'eva:slash-outline',
  'eva:smartphone-fill',
  'eva:smartphone-outline',
  'eva:smiling-face-fill',
  'eva:smiling-face-outline',
  'eva:speaker-fill',
  'eva:speaker-outline',
  'eva:square-fill',
  'eva:square-outline',
  'eva:star-fill',
  'eva:star-outline',
  'eva:stop-circle-fill',
  'eva:stop-circle-outline',
  'eva:sun-fill',
  'eva:sun-outline',
  'eva:swap-fill',
  'eva:swap-outline',
  'eva:sync-fill',
  'eva:sync-outline',
  'eva:text-fill',
  'eva:text-outline',
  'eva:thermometer-fill',
  'eva:thermometer-minus-fill',
  'eva:thermometer-minus-outline',
  'eva:thermometer-outline',
  'eva:thermometer-plus-fill',
  'eva:thermometer-plus-outline',
  'eva:toggle-left-fill',
  'eva:toggle-left-outline',
  'eva:toggle-right-fill',
  'eva:toggle-right-outline',
  'eva:trash-2-fill',
  'eva:trash-2-outline',
  'eva:trash-fill',
  'eva:trash-outline',
  'eva:trending-down-fill',
  'eva:trending-down-outline',
  'eva:trending-up-fill',
  'eva:trending-up-outline',
  'eva:tv-fill',
  'eva:tv-outline',
  'eva:twitter-fill',
  'eva:twitter-outline',
  'eva:umbrella-fill',
  'eva:umbrella-outline',
  'eva:undo-fill',
  'eva:undo-outline',
  'eva:unlock-fill',
  'eva:unlock-outline',
  'eva:upload-fill',
  'eva:upload-outline',
  'eva:video-fill',
  'eva:video-off-fill',
  'eva:video-off-outline',
  'eva:video-outline',
  'eva:volume-down-fill',
  'eva:volume-down-outline',
  'eva:volume-mute-fill',
  'eva:volume-mute-outline',
  'eva:volume-off-fill',
  'eva:volume-off-outline',
  'eva:volume-up-fill',
  'eva:volume-up-outline',
  'eva:wifi-fill',
  'eva:wifi-off-fill',
  'eva:wifi-off-outline',
  'eva:wifi-outline',
];

// Props interface for IconSelector component
interface IconSelectorProps {
  error?: boolean;
  helperText?: string;
}

export default function IconSelector({ error, helperText }: IconSelectorProps): React.JSX.Element {
  const { setValue, watch } = useFormContext();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const iconValue = watch('icon');

  // State for icon search and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [filteredIcons, setFilteredIcons] = useState<string[]>(POPULAR_ICONS);

  const ICONS_PER_PAGE = 36; // Show more icons per page

  // Filter icons based on search term
  useEffect(() => {
    setLoading(true);

    // Simulate API call delay
    const timer = setTimeout(() => {
      let results = [...POPULAR_ICONS];

      if (searchTerm) {
        results = results.filter((icon) => icon.toLowerCase().includes(searchTerm.toLowerCase()));
      }

      setFilteredIcons(results);
      setCurrentPage(1);
      setLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredIcons.length / ICONS_PER_PAGE);
  const displayedIcons = filteredIcons.slice(
    (currentPage - 1) * ICONS_PER_PAGE,
    currentPage * ICONS_PER_PAGE
  );

  const handleOpenPopover = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
    setSearchTerm('');
    setCurrentPage(1);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // No library filter needed

  const open = Boolean(anchorEl);

  return (
    <>
      <Stack spacing={1}>
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          Choose Icon
        </Typography>
        <Box
          component="button"
          type="button"
          onClick={handleOpenPopover}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            whiteSpace: 'nowrap',
            width: '100%',

            p: 2,
            border: (theme) =>
              `1px solid ${error ? theme.palette.error.main : theme.palette.divider}`,
            borderRadius: 2,
            bgcolor: 'background.paper',
            cursor: 'pointer',
            '&:hover': {
              bgcolor: 'action.hover',
            },
            '&:focus': {
              outline: 'none',
              borderColor: error ? 'error.main' : 'primary.main',
            },
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            {iconValue ? (
              <Iconify icon={iconValue} width={20} height={20} />
            ) : (
              <Iconify icon="eva:brush-fill" width={20} height={20} />
            )}
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: 80,
              }}
            >
              {iconValue || 'icon-name'}
            </Typography>
          </Stack>
          <Iconify icon="eva:chevron-down-fill" width={16} height={16} />
        </Box>
        {helperText && (
          <Typography
            variant="caption"
            sx={{
              color: error ? 'error.main' : 'text.secondary',
              mt: 0.5,
              ml: 1.5,
            }}
          >
            {helperText}
          </Typography>
        )}
      </Stack>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClosePopover}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        slotProps={{
          paper: {
            sx: {
              p: 2,
              width: 500,
              maxHeight: 600,
              overflow: 'hidden',
            },
          },
        }}
      >
        <Stack spacing={2}>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            Select Icon
          </Typography>

          {/* Search field */}
          <Paper sx={{ p: '2px 4px', display: 'flex', alignItems: 'center', width: '100%' }}>
            <InputBase
              sx={{ ml: 1, flex: 1, whiteSpace: 'nowrap' }}
              placeholder="Search icons..."
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyDown={(e) => {
                // Prevent form submission on Enter key
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
              inputProps={{ 'aria-label': 'search icons' }}
            />
            <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <Iconify icon="eva:search-fill" width={20} height={20} />
            </IconButton>
          </Paper>

          {/* Icons grid */}
          <Box sx={{ height: 350, overflow: 'auto', py: 1 }}>
            {loading ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                }}
              >
                <CircularProgress size={40} />
              </Box>
            ) : displayedIcons.length > 0 ? (
              <Grid container spacing={1}>
                {displayedIcons.map((icon) => (
                  <Grid item xs={3} key={icon}>
                    <IconButton
                      type="button"
                      onClick={() => {
                        setValue('icon', icon, { shouldValidate: true, shouldDirty: true });
                        handleClosePopover();
                      }}
                      sx={{
                        color: iconValue === icon ? 'primary.main' : 'text.secondary',
                        border: (theme) =>
                          iconValue === icon ? `solid 1px ${theme.palette.primary.main}` : 'none',
                        borderRadius: 1,
                        width: '100%',
                        height: 56,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                      title={icon}
                    >
                      <Iconify icon={icon} width={24} height={24} />
                    </IconButton>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  No icons found
                </Typography>
              </Box>
            )}
          </Box>

          {/* Pagination */}
          {totalPages > 1 && (
            <Stack spacing={1} alignItems="center">
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="medium"
                siblingCount={2}
                boundaryCount={2}
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  '& .MuiPaginationItem-root': {
                    margin: '0 2px',
                  },
                  '& .MuiPaginationItem-icon': {
                    // Fix for pagination arrow icons
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                }}
              />
              <Typography variant="caption" color="text.secondary">
                Page {currentPage} of {totalPages} • {filteredIcons.length} icons
              </Typography>
            </Stack>
          )}
        </Stack>
      </Popover>
    </>
  );
}
