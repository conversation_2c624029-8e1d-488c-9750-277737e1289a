import { Box, Container, Typography, I<PERSON><PERSON><PERSON>on, Stack } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';

import { AppContainer, AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';
import { useTranslation } from 'react-i18next';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { LoadingScreen } from 'src/components/loading-screen';
import CategoryForm from '../form/category-form';
import { CategoryFormValues, createCategorySchema } from '../form/category-schema';

// ----------------------------------------------------------------------

const CreateCategoryView = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();

  // Determine if we're in edit mode
  const isEditMode = Boolean(id);
  const categoryId = id ? parseInt(id, 10) : null;

  // Get API hooks directly for this component
  const { useGetCategory, useCreateCategory, useUpdateCategory } = useCategoriesApi();

  // API hook for fetching individual category
  const { data: categoryData, isLoading: isFetchingCategory, isError } = useGetCategory(categoryId || 0);

  // Create and Update mutations with proper loading states
  const { mutate: createCategory, isPending: isCreating } = useCreateCategory();
  const { mutate: updateCategory, isPending: isUpdating } = useUpdateCategory(categoryId || 0);

  // Combined loading state for the button
  const isButtonLoading = isCreating || isUpdating;

  // Helper function to convert color type to backend-compatible theme value (exactly 7 chars)
  const convertColorTypeToTheme = (colorType: string, customColor?: string): string => {
    if (colorType === 'custom') {
      // For custom colors, ensure exactly 7 characters
      const color = customColor || '#FF5733';
      if (color.length === 7) {
        return color; // Perfect length
      }
      if (color.length > 7) {
        return color.substring(0, 7); // Truncate to 7 chars
      }
      return color.padEnd(7, '0'); // Pad to 7 chars
    }

    // Map color type names to backend-compatible codes (exactly 7 characters)
    const colorTypeMap: Record<string, string> = {
      'primary': 'primary',      // 7 chars - OK
      'secondary': 'second1',    // 7 chars - shortened from 'secondary' (9 chars)
      'success': 'success',      // 7 chars - OK
      'warning': 'warning',      // 7 chars - OK
      'info': 'info-cl',         // 7 chars - extended from 'info' (4 chars)
      'error': 'error-c',        // 7 chars - extended from 'error' (5 chars)
    };

    return colorTypeMap[colorType] || colorType.substring(0, 7).padEnd(7, '0');
  };

  // Handle form submission
  const handleFormSubmit = (data: CategoryFormValues) => {
    // Convert form data to API request format
    const apiRequest = {
      name: data.name,
      description: data.description,
      icon: data.icon,
      theme: convertColorTypeToTheme(data.colorType, data.customColor),
    };

    if (isEditMode && categoryId) {
      // Update existing category
      updateCategory(apiRequest, {
        onSuccess: () => {
          // Navigate back to categories list on success
          navigate(paths.dashboard.categories.root);
        },
        onError: (error) => {
          console.error('Failed to update category:', error);
        },
      });
    } else {
      // Create new category
      createCategory(apiRequest, {
        onSuccess: () => {
          // Navigate back to categories list on success
          navigate(paths.dashboard.categories.root);
        },
        onError: (error) => {
          console.error('Failed to create category:', error);
        },
      });
    }
  };

  // Handle cancel - navigate back to categories list
  const handleCancel = () => {
    navigate(paths.dashboard.categories.root);
  };

  // Form methods
  const methods = useForm<CategoryFormValues>({
    mode: 'onChange',
    resolver: zodResolver(createCategorySchema(t)),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      colorType: 'primary',
      customColor: '#FF5733',
    },
  });

  // Helper function to convert backend theme value back to color type
  const convertThemeToColorType = (theme: string): 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error' | 'custom' => {
    if (theme?.startsWith('#')) {
      return 'custom';
    }

    // Map backend codes back to color type names
    const themeToColorTypeMap: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error'> = {
      'primary': 'primary',
      'second1': 'secondary',  // Map 'second1' back to 'secondary'
      'success': 'success',
      'warning': 'warning',
      'info-cl': 'info',       // Map 'info-cl' back to 'info'
      'error-c': 'error',      // Map 'error-c' back to 'error'
    };

    return themeToColorTypeMap[theme] || 'primary';
  };

  // Helper function to convert API category to form data
  const convertApiToFormData = (category: any): CategoryFormValues => {
    const isCustomColor = category.theme?.startsWith('#');
    return {
      name: category.name || '',
      description: category.description || '',
      icon: category.icon || '',
      colorType: convertThemeToColorType(category.theme),
      customColor: isCustomColor ? category.theme : '#FF5733',
    };
  };

  // Update form when category data is loaded (for edit mode)
  useEffect(() => {
    if (isEditMode && categoryData && !isFetchingCategory) {
      const formData = convertApiToFormData(categoryData);
      methods.reset(formData);
    }
  }, [categoryData, isFetchingCategory, isEditMode, methods]);

  const { handleSubmit } = methods;

  // Show loading screen while fetching category data in edit mode
  if (isEditMode && isFetchingCategory) {
    return <LoadingScreen />;
  }

  // Show error if category not found in edit mode
  if (isEditMode && isError) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error">
          Category not found
        </Typography>
        <AppButton
          variant="outlined"
          onClick={() => navigate(paths.dashboard.categories.root)}
          sx={{ mt: 2 }}
          label="Back to Categories"
        />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'grey.50',
        p: 3,
      }}
    >
      <Box sx={{ mb: 3 }}>
        <IconButton
          onClick={handleCancel}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Iconify icon="eva:arrow-back-fill" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            Back
          </Typography>
        </IconButton>
      </Box>

      <Form methods={methods} onSubmit={handleSubmit(handleFormSubmit)}>
        <AppContainer
          title={isEditMode ? t('categories.edit') : t('categories.createNew')}
          pageTitle={isEditMode ? t('categories.edit') : t('categories.createNew')}
          routeLinks={[
            {
              name: t('categories.title'),
              href: paths.dashboard.categories.root,
            },
            {
              name: isEditMode ? t('categories.edit') : t('categories.createNew'),
            },
          ]}
        >
          <Box
            sx={{
              width: { xs: '100%', sm: '80%', md: '80%' }, // Make it responsive
              mx: 'auto',
              my: '3%',
              bgcolor: 'divider',
              borderRadius: 2,
              p: 2,
              boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Container disableGutters maxWidth={false}>
              <Box sx={{ mb: 1 ,borderRadius:'50px'}}>
                <Typography color="rgba(15, 14, 17, 0.65)" variant="h4">
                  {isEditMode ? 'Edit your category details' : 'Add your team\'s template details'}
                </Typography>
              </Box>

              <Box
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  backgroundColor: 'white',
                  borderRadius: 2,
                }}
              >
                <CategoryForm />
              </Box>
            </Container>
          </Box>
        </AppContainer>

        {/* Create Button - positioned after AppContainer */}
        <Stack direction="row" justifyContent="flex-end" sx={{ mt: 4, px: 3 }}>
          <AppButton
            type="submit"
            variant="contained"
            color="primary"
            isLoading={isButtonLoading}
            label={isEditMode ? 'Update' : 'Create'}
            sx={{
              height: '35px',
              maxWidth: '15%',
              px: 4,
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
        </Stack>
      </Form>
    </Box>
  );
};

export default CreateCategoryView;