import { z } from 'zod';

// ----------------------------------------------------------------------

// Form validation schema
export const agentFormSchema = z
  .object({
    name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
    description: z
      .string()
      .min(1, 'Description is required')
      .max(500, 'Description must be less than 500 characters'),
    systemMessage: z
      .string()
      .min(1, 'System message is required')
      .min(10, 'systemMessage must be longer than or equal to 10 characters')
      .max(2000, 'System message must be less than 2000 characters'),

    type: z.enum(['SINGLE', 'MULTI'], {
      required_error: 'Type is required',
    }),
    status: z.enum(['ACTIVE', 'DISABLED'], {
      required_error: 'Status is required',
    }),
    category: z.string().min(1, 'Category is required'),
    toolsId: z.array(z.number()).optional(),
    model: z.enum(
      ['GPT_4O_MINI', 'GPT_4O', 'CLAUDE_3_7_SONNET', 'GEMINI_2_0_FLASH', 'GEMINI_1_5_FLASH'],
      {
        required_error: 'Model is required',
      }
    ),

    categoryId: z.coerce.number().optional(),
  })
  .transform((data) => ({
    ...data,
    categoryId: Number(data.category),
  }));

// Form values type
export type AgentFormValues = z.infer<typeof agentFormSchema>;

// Agent type options
export const AGENT_TYPE_OPTIONS = [
  {
    value: 'SINGLE',
    label: 'Single Agent',
    description: 'A standalone agent that works independently',
    icon: 'mdi:account-outline',
  },
  {
    value: 'MULTI',
    label: 'Multi Agent',
    description: 'An agent that works as part of a team',
    icon: 'mdi:account-group-outline',
  },
] as const;

// Original format for compatibility
export const TYPE_OPTIONS = AGENT_TYPE_OPTIONS.map((option) => ({
  value: option.value,
  label: option.label,
}));

// Agent status options
export const AGENT_STATUS_OPTIONS = [
  {
    value: 'ACTIVE',
    label: 'Active',
    description: 'Agent is active and available for use',
    color: 'success' as const,
  },
  {
    value: 'DISABLED',
    label: 'Disabled',
    description: 'Agent is disabled and not available for use',
    color: 'error' as const,
  },
] as const;

// Original format for compatibility
export const STATUS_OPTIONS = AGENT_STATUS_OPTIONS.map((option) => ({
  value: option.value,
  label: option.label,
}));

// LLM Model options
export const LLM_MODEL_OPTIONS = [
  {
    value: 'GPT_4O_MINI',
    label: 'GPT-4o Mini',
    description: 'Fast and efficient model for simple tasks',
    icon: 'hugeicons:chat-gpt',
    provider: 'OpenAI',
  },
  {
    value: 'GPT_4O',
    label: 'GPT-4o',
    description: 'Advanced model for complex reasoning tasks',
    icon: 'arcticons:openai-chatgpt',
    provider: 'OpenAI',
  },
  {
    value: 'CLAUDE_3_7_SONNET',
    label: 'Claude 3.5 Sonnet',
    description: "Anthropic's most intelligent model for complex tasks",
    icon: 'simple-icons:anthropic',
    provider: 'Anthropic',
  },
  {
    value: 'GEMINI_2_0_FLASH',
    label: 'Gemini 2.0 Flash',
    description: "Google's latest multimodal AI model",
    icon: 'ri:gemini-fill',
    provider: 'Google',
  },
  {
    value: 'GEMINI_1_5_FLASH',
    label: 'Gemini 1.5 Flash',
    description: 'Fast and efficient multimodal model',
    icon: 'ri:gemini-line',
    provider: 'Google',
  },
] as const;



// Form steps configuration
export const FORM_STEPS = [
  {
    id: 'details',
    label: 'Agent Details',
    description: 'Basic information about your agent',
    icon: 'mdi:information-outline',
    fields: ['name', 'description', 'systemMessage', 'type', 'status'] as const,
  },
  {
    id: 'category',
    label: 'Category',
    description: 'Select the category for your agent',
    icon: 'mdi:tag-outline',
    fields: ['category'] as const,
  },
  {
    id: 'tools',
    label: 'Tools',
    description: 'Choose tools for your agent',
    icon: 'mdi:tools',   
   fields: ['category'] as const,

    // fields: ['toolsId'] as const,
  },
  {
    id: 'model',
    label: 'LLM MODEL',
    description: 'Select the AI model to use',
    icon: 'mdi:brain',
    fields: ['model'] as const,
  },
] as const;

// Default form values
export const DEFAULT_FORM_VALUES: Partial<AgentFormValues> = {
  name: '',
  description: '',
  systemMessage: '',

  type: 'SINGLE',
  status: 'ACTIVE',
  category: '',
  toolsId: [],
  model: 'GPT_4O_MINI',

};

// Form field configurations
export const FORM_FIELD_CONFIG = {
  name: {
    label: 'Agent Name',
    placeholder: 'Enter a descriptive name for your agent',
    helperText: "Choose a clear, descriptive name that reflects the agent's purpose",
  },
  description: {
    label: 'Description',
    placeholder: 'Describe what your agent does and its main capabilities',
    helperText: "Provide a clear description of the agent's purpose and functionality",
    multiline: true,
    rows: 3,
  },
  systemMessage: {
    label: 'System Instructions',
    placeholder: 'Enter detailed instructions that define how the agent should behave and respond',
    helperText: "These instructions guide the agent's behavior and responses",
    multiline: true,
    rows: 4,
  },
} as const;
