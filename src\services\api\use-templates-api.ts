import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates
export const templateEndpoints = {
  adminList: '/admin/templates',
  adminDetails: '/admin/templates',
  publishStatus: '/admin/templates',
};

// Define the Template data type based on API response
export interface Template {
  id: number;
  creatorId: number;
  name: string;
  description: string;
  categoryId: number;
  type: 'SINGLE' | 'MULTI';
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  status: 'ACTIVE' | 'DISABLED';
  publishRequestStatus?: 'PENDING' | 'APPROVED' | 'REJECTED';
  visibility?: 'PUBLIC' | 'PRIVATE';
  createdAt: string;
  updatedAt: string;
  category: {
    id: number;
    name: string;
    description: string;
    icon: string;
    theme: string;
    createdAt: string;
    updatedAt: string;
  };
  systemMessage: string;
  templateTools?: {
    id: number;
    templateId: number;
    toolId: number;
    createdAt: string;
    tool: {
      id: number;
      name: string;
      description: string;
      createdAt: string;
      updatedAt: string;
    };
  }[];
}

// Define the API response types
export interface TemplatesListResponse {
  templates: Template[];
  total: number;
}

// Define the request body types
export interface CreateTemplateRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  toolsId?: number[];
  status: 'ACTIVE' | 'DISABLED';
}

export interface UpdateTemplateRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  toolsId?: number[];
  status: 'ACTIVE' | 'DISABLED';
}

// Define query parameters for list endpoint
export interface TemplatesQueryParams {
  take?: number;
  skip?: number;
  name?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
  visibility?: 'PUBLIC' | 'PRIVATE';
}

// Define publish status request body
export interface PublishStatusRequest {
  status: 'APPROVED' | 'REJECTED';
}

// Create a hook to use the templates API
export const useTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates (using admin endpoint with visibility filter)
  const useGetTemplates = (params?: TemplatesQueryParams) => {
    return apiServices.useGetListService<TemplatesListResponse, TemplatesQueryParams>({
      url: templateEndpoints.adminList,
      params,
    });
  };

  // Get a single template by ID
  const useGetTemplate = (id: number) => {
    return apiServices.useGetItemService<Template>({
      url: templateEndpoints.adminDetails,
      id: id.toString(),
    });
  };

  // Create a new template
  const useCreateTemplate = (onSuccess?: (data: Template) => void) => {
    return apiServices.usePostService<CreateTemplateRequest, Template>({
      url: templateEndpoints.adminList,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a template using PATCH
  const useUpdateTemplate = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<Partial<UpdateTemplateRequest>>({
      url: templateEndpoints.adminDetails,
      id: id.toString(),
      onSuccess,
      withFormData: false,
      queryKey: templateEndpoints.adminList + 'list',
    });
  };

  // Delete a template
  const useDeleteTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: templateEndpoints.adminDetails,
      urlAfterSuccess: templateEndpoints.adminList + 'list',
      onSuccess,
    });
  };

  // Update publish status for admin templates
  const useUpdatePublishStatus = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<PublishStatusRequest>({
      url: templateEndpoints.publishStatus,
      id: `${id}/publish-status`,
      onSuccess,
      withFormData: false,
      queryKey: templateEndpoints.adminList + 'list',
    });
  };

  return {
    useGetTemplates,
    useGetTemplate,
    useCreateTemplate,
    useUpdateTemplate,
    useDeleteTemplate,
    useUpdatePublishStatus,
  };
};
