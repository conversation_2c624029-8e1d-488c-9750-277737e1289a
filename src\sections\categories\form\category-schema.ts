import * as z from 'zod';

// Create schema with translation function
export const createCategorySchema = (t: any) => z.object({
  name: z.string().min(1, t('categories.validation.nameRequired')),
  description: z.string()
    .min(1, t('categories.validation.descriptionRequired'))
    .min(7, t('categories.validation.descriptionMinLength')),
  icon: z.string()
    .min(1, t('categories.validation.iconRequired'))
    .refine(
      (value) => {
        // Check if icon follows the pattern: library:icon-name
        // Examples: eva:home-fill, mdi:account, heroicons:user
        const iconPattern = /^[a-zA-Z0-9-]+:[a-zA-Z0-9-]+$/;
        return iconPattern.test(value);
      },
      { message: t('categories.validation.iconFormat') }
    ),
  colorType: z.enum(['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom'], {
    required_error: t('categories.validation.colorTypeRequired'),
    invalid_type_error: t('categories.validation.colorTypeRequired'),
  }),
  customColor: z
    .string()
    .optional()
    .refine(
      (value) => {
        // If customColor is provided, validate hex color format
        if (value && value.trim() !== '') {
          const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
          return hexPattern.test(value);
        }
        return true;
      },
      { message: t('categories.validation.invalidHexColor') }
    ),
}).refine(
  (data) => {
    // Additional validation: if colorType is custom, customColor must be provided
    if (data.colorType === 'custom') {
      return data.customColor && data.customColor.trim() !== '';
    }
    return true;
  },
  {
    message: t('categories.validation.customColorRequired'),
    path: ['customColor'], // This will show the error on the customColor field
  }
);

// Default schema for backward compatibility (without translations)
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string()
    .min(1, 'Description is required')
    .min(7, 'Description must be at least 7 characters'),
  icon: z.string()
    .min(1, 'Icon is required')
    .refine(
      (value) => {
        // Check if icon follows the pattern: library:icon-name
        const iconPattern = /^[a-zA-Z0-9-]+:[a-zA-Z0-9-]+$/;
        return iconPattern.test(value);
      },
      { message: 'Icon must be in valid format (e.g., eva:home-fill)' }
    ),
  colorType: z.enum(['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom'], {
    required_error: 'Please select a color for your category',
    invalid_type_error: 'Please select a color for your category',
  }),
  customColor: z
    .string()
    .optional()
    .refine(
      (value) => {
        // If customColor is provided, validate hex color format
        if (value && value.trim() !== '') {
          const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
          return hexPattern.test(value);
        }
        return true;
      },
      { message: 'Please enter a valid hex color (e.g., #FF5733)' }
    ),
}).refine(
  (data) => {
    // Additional validation: if colorType is custom, customColor must be provided
    if (data.colorType === 'custom') {
      return data.customColor && data.customColor.trim() !== '';
    }
    return true;
  },
  {
    message: 'Custom color is required when custom color type is selected',
    path: ['customColor'],
  }
);

export type CategoryFormValues = z.infer<typeof categorySchema>;
